<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <!-- تحديث إعدادات أنواع الطلبات لضمان الإعدادات الصحيحة -->
        <!-- Update request type settings to ensure correct configuration -->
        
        <!-- تحديث نوع طلب الصلاحيات -->
        <!-- Update permission request type -->
        <function model="bssic.request.type" name="write">
            <value model="bssic.request.type" eval="obj().search([('code', '=', 'permission')]).ids"/>
            <value eval="{
                'show_permission_fields': True,
                'active': True,
                'show_password_fields': False,
                'show_usb_fields': False,
                'show_extension_fields': False,
                'show_email_fields': False,
                'show_authorization_delegation_fields': False,
                'show_free_entry_fields': False
            }"/>
        </function>
        
        <!-- تحديث نوع طلب إعادة تعيين كلمة المرور -->
        <!-- Update password reset request type -->
        <function model="bssic.request.type" name="write">
            <value model="bssic.request.type" eval="obj().search([('code', '=', 'password_reset')]).ids"/>
            <value eval="{
                'show_password_fields': True,
                'active': True,
                'show_permission_fields': False,
                'show_usb_fields': False,
                'show_extension_fields': False,
                'show_email_fields': False,
                'show_authorization_delegation_fields': False,
                'show_free_entry_fields': False
            }"/>
        </function>
        
        <!-- تحديث نوع طلب USB -->
        <!-- Update USB request type -->
        <function model="bssic.request.type" name="write">
            <value model="bssic.request.type" eval="obj().search([('code', '=', 'usb')]).ids"/>
            <value eval="{
                'show_usb_fields': True,
                'active': True,
                'show_permission_fields': False,
                'show_password_fields': False,
                'show_extension_fields': False,
                'show_email_fields': False,
                'show_authorization_delegation_fields': False,
                'show_free_entry_fields': False
            }"/>
        </function>
        
        <!-- تحديث نوع طلب التمديد -->
        <!-- Update extension request type -->
        <function model="bssic.request.type" name="write">
            <value model="bssic.request.type" eval="obj().search([('code', '=', 'extension')]).ids"/>
            <value eval="{
                'show_extension_fields': True,
                'active': True,
                'show_permission_fields': False,
                'show_password_fields': False,
                'show_usb_fields': False,
                'show_email_fields': False,
                'show_authorization_delegation_fields': False,
                'show_free_entry_fields': False
            }"/>
        </function>
        
        <!-- تحديث نوع طلب البريد الإلكتروني -->
        <!-- Update email request type -->
        <function model="bssic.request.type" name="write">
            <value model="bssic.request.type" eval="obj().search([('code', '=', 'email')]).ids"/>
            <value eval="{
                'show_email_fields': True,
                'active': True,
                'show_permission_fields': False,
                'show_password_fields': False,
                'show_usb_fields': False,
                'show_extension_fields': False,
                'show_authorization_delegation_fields': False,
                'show_free_entry_fields': False
            }"/>
        </function>
        
        <!-- تحديث نوع طلب تفويض الصلاحيات -->
        <!-- Update authorization delegation request type -->
        <function model="bssic.request.type" name="write">
            <value model="bssic.request.type" eval="obj().search([('code', '=', 'authorization_delegation')]).ids"/>
            <value eval="{
                'show_authorization_delegation_fields': True,
                'active': True,
                'show_permission_fields': False,
                'show_password_fields': False,
                'show_usb_fields': False,
                'show_extension_fields': False,
                'show_email_fields': False,
                'show_free_entry_fields': False
            }"/>
        </function>
        
        <!-- تحديث نوع الطلب المجاني -->
        <!-- Update free entry request type -->
        <function model="bssic.request.type" name="write">
            <value model="bssic.request.type" eval="obj().search([('code', '=', 'free_entry')]).ids"/>
            <value eval="{
                'show_free_entry_fields': True,
                'active': True,
                'show_permission_fields': False,
                'show_password_fields': False,
                'show_usb_fields': False,
                'show_extension_fields': False,
                'show_email_fields': False,
                'show_authorization_delegation_fields': False
            }"/>
        </function>
        
        <!-- تحديث نوع الطلب التقني -->
        <!-- Update technical request type -->
        <function model="bssic.request.type" name="write">
            <value model="bssic.request.type" eval="obj().search([('code', '=', 'technical')]).ids"/>
            <value eval="{
                'active': True,
                'show_permission_fields': False,
                'show_password_fields': False,
                'show_usb_fields': False,
                'show_extension_fields': False,
                'show_email_fields': False,
                'show_authorization_delegation_fields': False,
                'show_free_entry_fields': False
            }"/>
        </function>
        
    </data>
</odoo>
